@import '../common/css/reset.less';
.settings-tab-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  background-color: #f5f5f5;
  .settings-sidebar {
    width: 300px;
    background-color: #fff;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 47px 40px;

    font-family: PingFang SC;
    font-size: 18px;
    font-weight: 500;

    /* Logo区域 */
    .sidebar-logo {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;

      img {
        width: 185px;
        height: 37px;
      }
    }

    .sidebar-nav {
      flex: 1;
      padding: 16px 0;
      .nav-item {
        display: flex;
        font-size: 18px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
        line-height: 24px;
        gap: 16px;
        padding: 12px 16px;
        background: #f2f2f3;
        border-radius: 8px 8px 8px 8px;
        cursor: pointer;
        align-items: center;
        margin-bottom: 10px;
        
        &.active {
          background: #e6f4ff;
          color: #1677ff;
        }
      }
    }
  }

  .settings-main {
    width: 900px;
    background-color: white;
    display: flex;
    flex-direction: column;

    .main-content {
      flex: 1;
      padding: 47px;
      max-width: 800px;
      margin: 0 auto;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .market {
        .market-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 32px;
          .header-title {
            font-size: 20px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
          }
        }
        .skills {
          font-size: 18px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
          margin-bottom: 29px;
        }
      }
    }
  }
}
.common-skills {
  display: flex;
  align-items: center;
  gap: 37px;
  flex-wrap: wrap;
  margin-bottom: 42px;
}
.skill-item {
  background: #f2f2f3;
  border: 1px solid #f2f2f3;
  border-radius: 8px;
  flex-flow: column wrap;
  justify-content: center;
  align-items: center;
  display: flex;
  min-width: 48px;
  height: 56px;
  padding: 0 4px;
  img {
    width: 24px;
    height: 24px;
  }
  .skill-item-title {
    font-size: 10px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #000000;
    line-height: 10px;
    margin-top: 5px;
  }
}
.skill-badge {
  position: absolute;
  right: -10px;
  top: -10px;
}
.skillModal {
  .ant-modal-content {
    padding: 0;
  }
  .ant-modal-header {
    padding: 18px 24px;
    border: 1px solid #f2f2f3;
    margin-bottom: 16px;
  }
  .ant-modal-body {
    padding: 0 24px 24px 24px;
  }
  .ant-upload-wrapper {
    text-align: center;
  }
  .ant-upload-disabled {
    cursor: pointer;
  }
}
.formModal {
  .ant-input:placeholder-shown {
    font-size: 14px;
  }
  .ant-form-item-label {
    font-size: 14px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1d222c;
    line-height: 22px;
    width: 100%;
    label {
      width: 100%;
    }
  }
  .ant-radio-wrapper {
    color: #565a61;
    span {
      padding-inline-start: 0;
    }
  }
}
.modal-footer {
  display: flex;
  gap: 8px;
  button {
    flex: 1;
  }
}
.fileDesc {
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #565a61;
  line-height: 22px;
}
.skillPrompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .skillPrompt-desc {
    font-size: 12px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    line-height: 22px;
    background-image: linear-gradient(270deg, #2249fe 0%, #b74ab1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-left: 4px;
  }
}
.noData {
  width: 100%;
  text-align: center;
  font-size: 12px;
}
.selectIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  .selectIcon-icon {
    width: 58px;
    height: 58px;
    background: #f2f2f3;
    border-radius: 8px 8px 8px 8px;
    opacity: 1;
    border: 1px solid #f2f2f3;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 24px;
      height: 24px;
    }
  }
  .selectIcon-title {
    font-size: 14px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #1d222c;
    line-height: 22px;
    margin-top: 8px;
  }
}
.iconList {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 23px;
  padding: 0 35px 0 35px;
  justify-content: space-between;
  .icon-item {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
.skill-item-action {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
}
.delete-desc {
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #6c6f76;
  line-height: 22px;
  margin-bottom: 32px;
  margin-top: 16px;
}
.delete-haeder {
  display: flex;
  align-items: center;
  justify-self: center;
}
.delete-title {
  font-size: 18px;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #1d222c;
  line-height: 28px;
  padding-left: 10px;
}