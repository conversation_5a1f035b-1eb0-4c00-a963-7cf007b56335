import { useState, useEffect } from 'react'
import { Button, Form, Input, Card, message, Space, Modal, List, Popconfirm } from 'antd/es'
import { MinusCircleOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { getUserId } from '@/src/common/utils/userConfigApi'
import deleteIcon from '@/src/common/skillIcon/deleteIcon.png'
import addIcon from '@/src/common/skillIcon/addIcon.png'

interface AgentConfig {
  id?: string
  name: string
  description: string
  AppKey: string
  AppID: string
  UserID: string
  ApiUrl: string
  user_env: string
  inputData: Array<{ key: string; value: string }>
  isConfig?: number // 1 for selected, 0 for unselected
}

const LOCAL_STORAGE_KEY = 'hiagent_configs'

export default () => {
  const [form] = Form.useForm()
  const [userId, setUserId] = useState<string>('')
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [modalVisible, setModalVisible] = useState<boolean>(false)
  const [responseModalVisible, setResponseModalVisible] = useState<boolean>(false)
  const [editMode, setEditMode] = useState<boolean>(false)
  const [currentAgentId, setCurrentAgentId] = useState<string | null>(null)
  const [configuredAgents, setConfiguredAgents] = useState<AgentConfig[]>([])
  const [availableAgents, setAvailableAgents] = useState<AgentConfig[]>([])
  const [selectedAgents, setSelectedAgents] = useState<AgentConfig[]>([])

  // 获取用户ID
  useEffect(() => {
    const fetchUserId = async () => {
      try {
        const id = await getUserId()
        setUserId(id)
        form.setFieldsValue({ UserID: id })
      } catch (error) {
        console.error('获取用户ID失败:', error)
        message.error('获取用户ID失败')
      }
    }

    fetchUserId()
  }, [])

  // 从localStorage加载数据
  useEffect(() => {
    const loadData = () => {
      try {
        const storedData = localStorage.getItem(LOCAL_STORAGE_KEY)
        if (storedData) {
          const agents: AgentConfig[] = JSON.parse(storedData)
          setConfiguredAgents(agents)
          setAvailableAgents(agents.filter(agent => agent.isConfig === 0))
          setSelectedAgents(agents.filter(agent => agent.isConfig === 1))
        }
      } catch (error) {
        console.error('从localStorage加载数据失败:', error)
        message.error('加载数据失败')
      }
    }

    loadData()
  }, [])

  // 保存数据到localStorage
  const saveDataToLocalStorage = (agents: AgentConfig[]) => {
    try {
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(agents))
      return true
    } catch (error) {
      console.error('保存数据到localStorage失败:', error)
      message.error('保存数据失败')
      return false
    }
  }

  // 处理测试连接
  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields()

      // 构造 InputData 对象
      const inputData: Record<string, string> = {}
      if (values.inputData && Array.isArray(values.inputData)) {
        values.inputData.forEach((item: { key: string; value: string }) => {
          if (item.key) {
            inputData[item.key] = item.value
          }
        })
      }

      // 构造请求体（不包含name和description）
      const requestBody = {
        AppKey: values.AppKey,
        AppID: values.AppID,
        InputData: JSON.stringify(inputData),
        UserID: values.UserID,
        user_env: values.user_env
      }

      setLoading(true)
      setResponse(null)

      // 发起请求
      const response = await fetch(values.ApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apiKey': values.AppKey
          // 如果API需要额外的认证头部，可以在这里添加
        },
        body: JSON.stringify(requestBody)
      })

      const responseData = await response.json()
      setResponse(responseData)
      setResponseModalVisible(true) // 显示响应结果弹窗

      if (response.ok) {
        message.success('请求成功')
      } else {
        message.error(`请求失败: ${response.status} ${response.statusText}`)
      }
    } catch (error: any) {
      console.error('请求出错:', error)
      message.error(`请求出错: ${error.message}`)
      setResponse({ error: error.message })
      setResponseModalVisible(true) // 显示响应结果弹窗
    } finally {
      setLoading(false)
    }
  }

  // 添加默认的 InputData 字段
  const addDefaultFields = () => {
    const defaultFields = [
      { key: 'question_txt', value: '' },
      { key: 'image_path', value: '' }
    ]

    form.setFieldsValue({
      inputData: defaultFields
    })
  }

  // 显示新增Agent模态框
  const showModal = (agent?: AgentConfig) => {
    form.resetFields()

    if (agent) {
      // 编辑模式
      setEditMode(true)
      setCurrentAgentId(agent.id || null)
      form.setFieldsValue({
        name: agent.name,
        description: agent.description,
        AppKey: agent.AppKey,
        AppID: agent.AppID,
        UserID: agent.UserID,
        user_env: agent.user_env,
        ApiUrl: agent.ApiUrl,
        inputData: agent.inputData
      })
    } else {
      // 新增模式
      setEditMode(false)
      setCurrentAgentId(null)
      form.setFieldsValue({
        ApiUrl: 'http://*************:3000/llmpf/api/proxy/api/v1/sync_run_app_workflow',
        UserID: userId,
        user_env: 'dev'
      })
      addDefaultFields()
    }

    setModalVisible(true)
  }

  // 保存新Agent配置
  const handleSaveAgent = async () => {
    try {
      const values = await form.validateFields()

      const agentData: AgentConfig = {
        id: editMode && currentAgentId ? currentAgentId : Date.now().toString(),
        name: values.name,
        description: values.description,
        AppKey: values.AppKey,
        AppID: values.AppID,
        UserID: values.UserID,
        user_env: values.user_env,
        ApiUrl: values.ApiUrl,
        inputData: values.inputData,
        isConfig: editMode ?
          [...selectedAgents, ...availableAgents].find(a => a.id === currentAgentId)?.isConfig || 0 :
          0
      }

      let updatedAgents: AgentConfig[]
      if (editMode && currentAgentId) {
        // 更新现有agent
        updatedAgents = configuredAgents.map(agent =>
          agent.id === currentAgentId ? agentData : agent
        )
      } else {
        // 添加新agent
        updatedAgents = [...configuredAgents, agentData]
      }

      // 保存到localStorage
      if (saveDataToLocalStorage(updatedAgents)) {
        setConfiguredAgents(updatedAgents)
        setAvailableAgents(updatedAgents.filter(agent => agent.isConfig === 0))
        setSelectedAgents(updatedAgents.filter(agent => agent.isConfig === 1))

        setModalVisible(false)
        form.resetFields()
        message.success(editMode ? 'Agent更新成功' : 'Agent配置已保存')
      }
    } catch (error: any) {
      console.error('保存失败:', error)
      message.error('表单验证失败，请检查输入')
    }
  }

  // 删除Agent
  const handleDeleteAgent = (agentId: string) => {
    const updatedAgents = configuredAgents.filter(agent => agent.id !== agentId)

    // 保存到localStorage
    if (saveDataToLocalStorage(updatedAgents)) {
      setConfiguredAgents(updatedAgents)
      setAvailableAgents(updatedAgents.filter(agent => agent.isConfig === 0))
      setSelectedAgents(updatedAgents.filter(agent => agent.isConfig === 1))

      message.success('Agent删除成功')
    }
  }

  // 将Agent添加到已选列表
  const handleAddAgent = (agent: AgentConfig) => {
    const updatedAgents = configuredAgents.map(a =>
      a.id === agent.id ? { ...agent, isConfig: 1 } : a
    )

    // 保存到localStorage
    if (saveDataToLocalStorage(updatedAgents)) {
      setConfiguredAgents(updatedAgents)
      setAvailableAgents(updatedAgents.filter(a => a.isConfig === 0))
      setSelectedAgents(updatedAgents.filter(a => a.isConfig === 1))
    }
  }

  // 将Agent从已选列表移除
  const handleRemoveAgent = (agent: AgentConfig) => {
    const updatedAgents = configuredAgents.map(a =>
      a.id === agent.id ? { ...agent, isConfig: 0 } : a
    )

    // 保存到localStorage
    if (saveDataToLocalStorage(updatedAgents)) {
      setConfiguredAgents(updatedAgents)
      setAvailableAgents(updatedAgents.filter(a => a.isConfig === 0))
      setSelectedAgents(updatedAgents.filter(a => a.isConfig === 1))
    }
  }

  return (
    <div className="market">
      <div className="market-header">
        <span className="header-title">HiAgent 集成</span>
        <Button
          type="primary"
          style={{
            height: 32,
            borderRadius: 4,
            backgroundColor: '#1677FF',
            fontSize: 14,
            lineHeight: '22px',
          }}
          onClick={() => showModal()}
        >
          新增agent
        </Button>
      </div>

      {/* 已选配Agent列表 */}
      <div className="skills">已选配agent</div>
      <div className="common-skills">
        {selectedAgents.length > 0 ? (
          selectedAgents.map((agent) => (
            <div key={agent.id} style={{ position: 'relative' }}>
              <div className="skill-item">
                <div className="skill-item-title">{agent.name || agent.AppID}</div>
              </div>
              <div className="skill-item-action" style={{ marginTop: 8, display: 'flex', justifyContent: 'center', gap: 4 }}>
                <Popconfirm
                  title="确定删除此Agent吗？"
                  onConfirm={() => handleDeleteAgent(agent.id!)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    size="small"
                    style={{ fontSize: 8, height: 16, padding: '0 6px' }}
                  >
                    删除
                  </Button>
                </Popconfirm>
                <Button
                  type='primary'
                  size="small"
                  onClick={() => showModal(agent)}
                  style={{ fontSize: 8, height: 16, padding: '0 6px' }}
                >
                  编辑
                </Button>

              </div>
              <div style={{ position: 'absolute', right: -10, top: -10 }} onClick={() => handleRemoveAgent(agent)}>
                <img src={deleteIcon} alt="remove" />
              </div>
            </div>
          ))
        ) : (
          <div className="noData">暂无数据</div>
        )}
      </div>

      {/* 可选配Agent列表 */}
      <div className="skills">可选配agent</div>
      <div className="common-skills">
        {availableAgents.length > 0 ? (
          availableAgents.map((agent) => (
            <div key={agent.id} style={{ position: 'relative' }}>
              <div className="skill-item">
                <div className="skill-item-title">{agent.name || agent.AppID}</div>
              </div>
              <div className="skill-item-action" style={{ marginTop: 8, display: 'flex', justifyContent: 'center', gap: 4 }}>
                <Popconfirm
                  title="确定删除此Agent吗？"
                  onConfirm={() => handleDeleteAgent(agent.id!)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    size="small"
                    style={{ fontSize: 8, height: 16, padding: '0 6px' }}
                  >
                    删除
                  </Button>
                </Popconfirm>
                <Button
                  type='primary'
                  size="small"
                  onClick={() => showModal(agent)}
                  style={{ fontSize: 8, height: 16, padding: '0 6px' }}
                >
                  编辑
                </Button>

              </div>
              <div style={{ position: 'absolute', right: -10, top: -10 }} onClick={() => handleAddAgent(agent)}>
                <img src={addIcon} alt="add" />
              </div>
            </div>
          ))
        ) : (
          <div className="noData">暂无数据</div>
        )}
      </div>

      {/* 新增/编辑Agent模态框 */}
      <Modal
        title={editMode ? "编辑Agent" : "新增Agent"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button key="test" loading={loading} onClick={handleTestConnection}>
            测试连通性
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveAgent}>
            保存
          </Button>
        ]}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="Agent名称"
            rules={[{ required: true, message: '请输入Agent名称' }]}
          >
            <Input placeholder="请输入Agent名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Agent描述"
            rules={[{ required: true, message: '请输入Agent描述' }]}
          >
            <Input.TextArea placeholder="请输入Agent描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="AppKey"
            label="AppKey"
            rules={[{ required: true, message: '请输入 AppKey' }]}
          >
            <Input placeholder="请输入 AppKey" />
          </Form.Item>

          <Form.Item
            name="AppID"
            label="AppID"
            rules={[{ required: true, message: '请输入 AppID' }]}
          >
            <Input placeholder="请输入 AppID" />
          </Form.Item>

          <Form.Item
            name="UserID"
            label="UserID"
            rules={[{ required: true, message: '请输入 UserID' }]}
          >
            <Input placeholder="请输入 UserID" defaultValue={userId} />
          </Form.Item>

          <Form.Item
            name="user_env"
            label="User Environment"
            rules={[{ required: true, message: '请输入 User Environment' }]}
          >
            <Input placeholder="请输入 User Environment" />
          </Form.Item>

          <Form.Item
            name="ApiUrl"
            label="API 地址"
            rules={[{ required: true, message: '请输入 API 地址' }]}
          >
            <Input placeholder="请输入 API 地址" />
          </Form.Item>

          <Form.List name="inputData">
            {(fields, { add, remove }) => (
              <>
                <div style={{ marginBottom: 16 }}>
                  <Space>
                    <span>InputData 字段配置</span>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                    >
                      添加字段
                    </Button>
                    <Button
                      onClick={addDefaultFields}
                    >
                      添加默认字段
                    </Button>
                  </Space>
                </div>

                {fields.map(({ key, name, ...restField }) => (
                  <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                    <Form.Item
                      {...restField}
                      name={[name, 'key']}
                      rules={[{ required: true, message: '请输入字段名' }]}
                    >
                      <Input placeholder="字段名" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'value']}
                      rules={[{ required: true, message: '请输入字段值' }]}
                    >
                      <Input placeholder="字段值" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
              </>
            )}
          </Form.List>
        </Form>
      </Modal>

      {/* 响应结果弹窗 */}
      <Modal
        title="响应结果"
        open={responseModalVisible}
        onCancel={() => setResponseModalVisible(false)}
        footer={[
          <Button
            key="copy"
            onClick={() => navigator.clipboard.writeText(JSON.stringify(response, null, 2))}
          >
            复制响应
          </Button>,
          <Button
            key="close"
            type="primary"
            onClick={() => setResponseModalVisible(false)}
          >
            关闭
          </Button>
        ]}
        width={800}
      >
        <pre style={{
          background: '#f5f5f5',
          padding: 16,
          borderRadius: 4,
          maxHeight: 400,
          overflow: 'auto',
          whiteSpace: 'pre-wrap'
        }}>
          {response ? JSON.stringify(response, null, 2) : '暂无响应数据'}
        </pre>
      </Modal>
    </div>
  )
}