import { useState } from 'react'
import { ImgIcon } from '@/src/common/Icons'
import logo from '@/src/common/skillIcon/marketLogo.png'
import marketSkill from '@/src/common/skillIcon/marketSkill.png'
import HiAgent from './hiagent'
import SkillsMarketContent from './SkillsMarketContent'
import './skillsmarket.less'

export default () => {
  const [activeTab, setActiveTab] = useState('market')

  return (
    <>
      <div className="settings-tab-page">
        <div className="settings-sidebar">
          <div className="sidebar-logo">
            <img src={logo} alt="logo" />
          </div>
          <div className="sidebar-nav">
            <div 
              className={`nav-item ${activeTab === 'market' ? 'active' : ''}`} 
              onClick={() => setActiveTab('market')}
            >
              <ImgIcon src={marketSkill} width={20} height={20} />
              技能市场
            </div>
            <div 
              className={`nav-item ${activeTab === 'hiagent' ? 'active' : ''}`} 
              onClick={() => setActiveTab('hiagent')}
            >
              <ImgIcon src={marketSkill} width={20} height={20} />
              HiAgent
            </div>
          </div>
        </div>
        <div className="settings-main">
          <div className="main-content">
            {activeTab === 'market' && <SkillsMarketContent />}
            {activeTab === 'hiagent' && <HiAgent />}
          </div>
        </div>
      </div>
    </>
  )
}
